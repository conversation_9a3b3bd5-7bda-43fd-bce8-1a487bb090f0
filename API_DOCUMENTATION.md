# Zaptra API Documentation

This document provides a comprehensive list of all API endpoints available in the Zaptra application, organized by module and functionality.

## Table of Contents
1. [Core API Endpoints](#core-api-endpoints)
2. [Module APIs](#module-apis)
   - [Dashboard](#dashboard)
   - [Wpbox](#wpbox)
   - [Translatechat](#translatechat)
   - [Smswpbox](#smswpbox)
   - [Reminders](#reminders)
   - [Blog](#blog)
   - [Emailwpbox](#emailwpbox)
   - [Agents](#agents)
   - [Contacts](#contacts)
   - [Flowmaker](#flowmaker)
   - [Flowiseai](#flowiseai)
   - [Journies](#journies)
   - [Themes](#themes)
   - [Vendorlinks](#vendorlinks)
   - [Voiceflow](#voiceflow)
   - [Woolist](#woolist)
   - [Shopifylist](#shopifylist)
   - [StripehSubscribe](#stripesubscribe)
   - [RazorpaySubscribe](#razorpaysubscribe)
   - [Razorpay](#razorpay)
   - [Embedwhatsapp](#embedwhatsapp)
   - [Embeddedlogin](#embeddedlogin)
   - [Wpboxlanding](#wpboxlanding)

## Core API Endpoints

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/user | Get authenticated user details | Bearer Token |
| POST   | /api/login | User login | - |
| POST   | /api/register | Register new user | - |
| POST   | /api/logout | Logout user | Bearer Token |
| GET    | /api/companies | List companies | Bearer Token |
| POST   | /api/companies | Create new company | Bearer Token |
| GET    | /api/companies/{id} | Get company details | Bearer Token |
| PUT    | /api/companies/{id} | Update company | Bearer Token |
| DELETE | /api/companies/{id} | Delete company | Bearer Token |

## Module APIs

### Dashboard

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/dashboard | Get dashboard statistics | Bearer Token |
| GET    | /api/dashboard/summary | Get summary data | Bearer Token |

### Wpbox

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/wpbox/sendmessage | Send WhatsApp message | Bearer Token |
| GET    | /api/wpbox/getTemplates | Get message templates | Bearer Token |
| GET    | /api/wpbox/getGroups | Get WhatsApp groups | Bearer Token |
| GET    | /api/wpbox/getCampaigns | Get campaigns | Bearer Token |
| GET    | /api/wpbox/getContacts | Get contacts | Bearer Token |
| POST   | /api/wpbox/makeContact | Create contact | Bearer Token |
| POST   | /api/wpbox/sendcampaigns | Send campaign messages | Bearer Token |
| GET    | /api/wpbox/getSingleContact | Get single contact | Bearer Token |
| POST   | /api/wpbox/getConversations/{lastmessagetime} | Get conversations | Bearer Token |
| POST   | /api/wpbox/getMessages | Get messages | Bearer Token |

### Translatechat

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/translate/convert-style | Convert message style | Bearer Token |
| GET    | /api/translate/summarize-chat/{chat_id} | Summarize chat | Bearer Token |
| POST   | /api/translate/ask-ai | Ask AI a question | Bearer Token |

### Smswpbox

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/smswpbox/send | Send SMS | Bearer Token |
| GET    | /api/smswpbox/templates | Get SMS templates | Bearer Token |
| GET    | /api/smswpbox/balance | Get SMS balance | Bearer Token |
| GET    | /api/smswpbox/delivery-status/{messageId} | Get delivery status | Bearer Token |

### Reminders

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/reminders/create | Create reminder | Bearer Token |
| GET    | /api/reminders/list | List reminders | Bearer Token |
| PUT    | /api/reminders/{id} | Update reminder | Bearer Token |
| DELETE | /api/reminders/{id} | Delete reminder | Bearer Token |
| POST   | /api/reminders/reservation/makeReservation | Make reservation | Bearer Token |

### Blog

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/blog | List all blog posts | Public |
| GET    | /api/blog/{slug} | Get single blog post | Public |
| POST   | /api/blog | Create blog post | Bearer Token |
| PUT    | /api/blog/{id} | Update blog post | Bearer Token |
| DELETE | /api/blog/{id} | Delete blog post | Bearer Token |

### Emailwpbox

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/emailwpbox/templates | Get email templates | Bearer Token |
| POST   | /api/emailwpbox/send | Send email | Bearer Token |
| GET    | /api/emailwpbox/campaigns | Get email campaigns | Bearer Token |
| POST   | /api/emailwpbox/campaigns | Create email campaign | Bearer Token |

### Agents

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/agents | List agents | Bearer Token |
| POST   | /api/agents | Create agent | Bearer Token |
| GET    | /api/agents/{id} | Get agent details | Bearer Token |
| PUT    | /api/agents/{id} | Update agent | Bearer Token |
| DELETE | /api/agents/{id} | Delete agent | Bearer Token |

### Contacts

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/contacts | List contacts | Bearer Token |
| POST   | /api/contacts | Create contact | Bearer Token |
| GET    | /api/contacts/{id} | Get contact details | Bearer Token |
| PUT    | /api/contacts/{id} | Update contact | Bearer Token |
| DELETE | /api/contacts/{id} | Delete contact | Bearer Token |
| POST   | /api/contacts/import | Import contacts | Bearer Token |
| GET    | /api/contacts/export | Export contacts | Bearer Token |

### Flowmaker

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/flowmaker/flows | List flows | Bearer Token |
| POST   | /api/flowmaker/flows | Create flow | Bearer Token |
| GET    | /api/flowmaker/flows/{id} | Get flow details | Bearer Token |
| PUT    | /api/flowmaker/flows/{id} | Update flow | Bearer Token |
| DELETE | /api/flowmaker/flows/{id} | Delete flow | Bearer Token |
| POST   | /api/flowmaker/execute/{id} | Execute flow | Bearer Token |

### Flowiseai

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/flowiseai/chat | Chat with Flowise AI | Bearer Token |
| GET    | /api/flowiseai/flows | List available flows | Bearer Token |

### Journies

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/journies | List customer journeys | Bearer Token |
| POST   | /api/journies | Create journey | Bearer Token |
| GET    | /api/journies/{id} | Get journey details | Bearer Token |
| PUT    | /api/journies/{id} | Update journey | Bearer Token |
| DELETE | /api/journies/{id} | Delete journey | Bearer Token |

### Themes

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/themes | List available themes | Bearer Token |
| POST   | /api/themes/activate | Activate theme | Bearer Token |
| POST   | /api/themes/customize | Customize theme | Bearer Token |

### Vendorlinks

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/vendorlinks | List vendor links | Bearer Token |
| POST   | /api/vendorlinks | Create vendor link | Bearer Token |

### Voiceflow

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/voiceflow/interact | Interact with Voiceflow | Bearer Token |
| GET    | /api/voiceflow/sessions | List sessions | Bearer Token |

### Woolist

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/woolist/products | List WooCommerce products | Bearer Token |
| POST   | /api/woolist/sync | Sync with WooCommerce | Bearer Token |

### Shopifylist

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/shopifylist/products | List Shopify products | Bearer Token |
| POST   | /api/shopifylist/sync | Sync with Shopify | Bearer Token |

### Stripesubscribe

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/stripesubscribe/subscribe | Create subscription | Bearer Token |
| POST   | /api/stripesubscribe/cancel | Cancel subscription | Bearer Token |
| GET    | /api/stripesubscribe/plans | List subscription plans | Bearer Token |

### Razorpaysubscribe

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/razorpaysubscribe/subscribe | Create subscription | Bearer Token |
| POST   | /api/razorpaysubscribe/cancel | Cancel subscription | Bearer Token |
| GET    | /api/razorpaysubscribe/plans | List subscription plans | Bearer Token |

### Razorpay

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/razorpay/create-order | Create payment order | Bearer Token |
| POST   | /api/razorpay/verify-payment | Verify payment | Bearer Token |
| GET    | /api/razorpay/payments | List payments | Bearer Token |

### Embedwhatsapp

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| GET    | /api/embedwhatsapp/widget | Get WhatsApp widget code | Bearer Token |
| POST   | /api/embedwhatsapp/send | Send message via widget | Public |

### Embeddedlogin

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/embeddedlogin/auth | Authenticate user | - |
| GET    | /api/embeddedlogin/session | Check session | Bearer Token |

### Wpboxlanding

| Method | Endpoint | Description | Authentication |
|--------|----------|-------------|----------------|
| POST   | /api/wpboxlanding/leads | Submit lead form | Public |
| GET    | /api/wpboxlanding/stats | Get landing page stats | Bearer Token |

## Authentication

Most endpoints require authentication using a Bearer token. Include the token in the `Authorization` header:

```
Authorization: Bearer your_api_token_here
```

## Rate Limiting

API requests are rate limited to 60 requests per minute per IP address. Exceeding this limit will result in a `429 Too Many Requests` response.

## Error Handling

Standard HTTP status codes are used to indicate success or failure of API requests:

- `200 OK` - Request was successful
- `201 Created` - Resource was created successfully
- `400 Bad Request` - Invalid request format or parameters
- `401 Unauthorized` - Authentication failed or not provided
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation error
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## Pagination

Endpoints that return lists of resources support pagination using the following query parameters:

- `page` - Page number (default: 1)
- `per_page` - Items per page (default: 15, max: 100)

Example response with pagination metadata:

```json
{
  "data": [...],
  "meta": {
    "current_page": 1,
    "from": 1,
    "last_page": 5,
    "path": "https://api.example.com/resource",
    "per_page": 15,
    "to": 15,
    "total": 75
  }
}
```

## Webhooks

Zaptra can send webhook notifications for various events. To set up webhooks, configure the webhook URL in the admin panel.

### Webhook Events

- `message.received` - When a new message is received
- `message.sent` - When a message is successfully sent
- `contact.created` - When a new contact is created
- `contact.updated` - When a contact is updated
- `campaign.started` - When a campaign is started
- `campaign.completed` - When a campaign is completed
- `payment.received` - When a payment is received
- `subscription.created` - When a new subscription is created
- `subscription.cancelled` - When a subscription is cancelled

### Webhook Payload

Webhook payloads include the event type, timestamp, and relevant data. Example:

```json
{
  "event": "message.received",
  "timestamp": "2023-07-15T10:30:00Z",
  "data": {
    "message_id": "msg_12345",
    "contact_id": "cont_67890",
    "content": "Hello, world!",
    "channel": "whatsapp",
    "received_at": "2023-07-15T10:29:58Z"
  }
}
```

## Changelog

### v1.0.0 (2023-07-15)
- Initial API release
- Core modules and endpoints
- Basic authentication and rate limiting
- Webhook support
